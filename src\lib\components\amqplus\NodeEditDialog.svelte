<script>
	import { <PERSON><PERSON>, <PERSON>alogContent, <PERSON>alogD<PERSON><PERSON>, <PERSON><PERSON>Footer, <PERSON>alogHeader, DialogTitle } from '$lib/components/ui/dialog';
	import { Input } from '$lib/components/ui/input';
	import * as Select from '$lib/components/ui/select/index.js';
	import { Checkbox } from '$lib/components/ui/checkbox';
	import { Button } from '$lib/components/ui/button';
	import { Label } from '$lib/components/ui/label';
	import RangeSlider from 'svelte-range-slider-pips';
	import { openModal, closeModal } from '$lib/stores/modalStore.js';

	let {
		open = $bindable(false),
		nodeData = $bindable(null),
		onSave = () => {}
	} = $props();

	// Local state for editing
	let editedValue = $state(null);
	let isValid = $state(true);
	let validationMessage = $state('');

	// Generate unique ID for this dialog
	const dialogId = `node-edit-dialog-${Math.random().toString(36).substr(2, 9)}`;

	// Get node color for slider styling
	const getNodeColor = () => {
		return nodeData?.color || '#6366f1'; // Default to indigo if no color
	};

	// Handle body overflow when modal is open
	$effect(() => {
		if (typeof document !== 'undefined') {
			if (open) {
				document.body.style.overflow = 'hidden';
			} else {
				document.body.style.overflow = '';
			}
		}

		// Cleanup on unmount
		return () => {
			if (typeof document !== 'undefined') {
				document.body.style.overflow = '';
			}
		};
	});

	// Simple deep clone function to avoid structuredClone issues
	function deepClone(obj) {
		if (obj === null || typeof obj !== 'object') {
			return obj;
		}
		if (obj instanceof Date) {
			return new Date(obj.getTime());
		}
		if (Array.isArray(obj)) {
			return obj.map(item => deepClone(item));
		}
		const cloned = {};
		for (const key in obj) {
			if (obj.hasOwnProperty(key)) {
				cloned[key] = deepClone(obj[key]);
			}
		}
		return cloned;
	}

	// Manage modal state
	$effect(() => {
		if (open) {
			openModal(dialogId);
		} else {
			closeModal(dialogId);
		}
	});

	// Watch for nodeData changes to initialize editedValue
	$effect(() => {
		if (nodeData && open) {
			// Initialize editedValue based on the setting type and current value
			const settingId = nodeData.id?.replace('-setting', '');
			const config = settingConfigs[settingId];

			if (config && config.type === 'number-with-random') {
				// Handle number-with-random types
				if (typeof nodeData.currentValue === 'object' && nodeData.currentValue.random !== undefined) {
					editedValue = deepClone(nodeData.currentValue);
				} else if (typeof nodeData.currentValue === 'object' && nodeData.currentValue.value !== undefined) {
					editedValue = deepClone(nodeData.currentValue);
				} else {
					// Convert simple number to number-with-random structure
					editedValue = { random: false, value: nodeData.currentValue, min: config.min, max: config.max };
				}
			} else if (config && config.type === 'select-with-random') {
				// Handle select-with-random types
				if (typeof nodeData.currentValue === 'object' && nodeData.currentValue.random !== undefined) {
					editedValue = deepClone(nodeData.currentValue);
				} else if (typeof nodeData.currentValue === 'object' && nodeData.currentValue.value !== undefined) {
					editedValue = deepClone(nodeData.currentValue);
				} else {
					// Convert simple value to select-with-random structure
					const options = {};
					config.options.forEach(opt => options[opt.value] = false);
					editedValue = { random: false, value: nodeData.currentValue, options };
				}
			} else {
				editedValue = deepClone(nodeData.currentValue);
			}
		}
	});

	// Setting type configurations
	const settingConfigs = {
		// Mode Zone Settings
		'scoring': {
			type: 'select',
			label: 'Scoring Method',
			options: [
				{ value: 'count', label: 'Count' },
				{ value: 'hint', label: 'Hint' },
				{ value: 'speed', label: 'Speed' }
			],
			default: 'count'
		},
		'answering': {
			type: 'select',
			label: 'Answering Method',
			options: [
				{ value: 'typing', label: 'Typing' },
				{ value: 'mix', label: 'Mix' },
				{ value: 'multiple-choice', label: 'Multiple Choice' }
			],
			default: 'typing'
		},
		
		// General Zone Settings
		'players': {
			type: 'number',
			label: 'Number of Players',
			min: 1,
			max: 100,
			default: 8
		},
		'team-size': {
			type: 'number',
			label: 'Team Size',
			min: 1,
			max: 8,
			default: 1
		},
		'songs': {
			type: 'number-with-random',
			label: 'Number of Songs',
			min: 5,
			max: 100,
			default: 20,
			allowRandom: true
		},
		'watched-distribution': {
			type: 'select',
			label: 'Watched Distribution',
			options: [
				{ value: 'random', label: 'Random' },
				{ value: 'equal', label: 'Equal' }
			],
			default: 'random'
		},

		// Quiz Zone Settings
		'guess-time': {
			type: 'number-with-random',
			label: 'Guess Time (seconds)',
			min: 1,
			max: 60,
			default: 20,
			allowRandom: true
		},
		'extra-time': {
			type: 'number-with-random',
			label: 'Extra Guess Time (seconds)',
			min: 0,
			max: 15,
			default: 0,
			allowRandom: true
		},
		'sample-point': {
			type: 'range',
			label: 'Sample Point (%)',
			min: 0,
			max: 100,
			default: { start: 0, end: 100 }
		},
		'playback-speed': {
			type: 'select-with-random',
			label: 'Playback Speed',
			options: [
				{ value: 1, label: '1x' },
				{ value: 1.5, label: '1.5x' },
				{ value: 2, label: '2x' },
				{ value: 4, label: '4x' }
			],
			default: 1,
			allowRandom: true
		},
		'modifiers': {
			type: 'checkboxes',
			label: 'Modifiers',
			options: [
				{ key: 'skipGuessing', label: 'Skip Guessing', default: true },
				{ key: 'skipResults', label: 'Skip Results', default: true },
				{ key: 'queueing', label: 'Queueing', default: true }
			]
		},

		// Anime Zone Settings
		'anime-type': {
			type: 'checkboxes',
			label: 'Anime Types',
			options: [
				{ key: 'tv', label: 'TV', default: true },
				{ key: 'movie', label: 'Movie', default: true },
				{ key: 'ova', label: 'OVA', default: true },
				{ key: 'ona', label: 'ONA', default: true },
				{ key: 'special', label: 'Special', default: true }
			]
		},

		// New General Zone Settings
		'song-types-selection': {
			type: 'complex-song-types-selection',
			label: 'Song Types & Selection',
			default: {
				songTypes: { openings: { enabled: true, percentage: 50 }, endings: { enabled: true, percentage: 50 }, inserts: { enabled: false, percentage: 0 } },
				songSelection: { random: 0, mix: 0, watched: 100 },
				mode: 'percentage'
			}
		},
		'song-categories': {
			type: 'complex-song-categories',
			label: 'Song Categories',
			default: {
				openings: { standard: true, instrumental: true, chanting: true, character: true },
				endings: { standard: true, instrumental: true, chanting: true, character: true },
				inserts: { standard: true, instrumental: true, chanting: true, character: true },
				mode: 'all',
				counts: { openings: 10, endings: 10, inserts: 0 },
				percentages: { openings: 50, endings: 50, inserts: 0 }
			}
		},

		// New Quiz Zone Settings
		'song-difficulty': {
			type: 'complex-song-difficulty',
			label: 'Song Difficulty',
			default: {
				easy: { enabled: true, percentage: 33.33 },
				medium: { enabled: true, percentage: 33.33 },
				hard: { enabled: true, percentage: 33.34 },
				mode: 'percentage'
			}
		},

		// New Anime Zone Settings
		'player-score': {
			type: 'complex-score-range',
			label: 'Player Score',
			min: 1,
			max: 10,
			default: { min: 1, max: 10, mode: 'range', percentages: {} }
		},
		'anime-score': {
			type: 'complex-score-range',
			label: 'Anime Score',
			min: 2,
			max: 10,
			default: { min: 2, max: 10, mode: 'range', percentages: {} }
		},
		'vintage': {
			type: 'complex-vintage',
			label: 'Vintage',
			default: { ranges: [{ from: { season: 'Winter', year: 1944 }, to: { season: 'Fall', year: 2025 } }] }
		},
		'genres': {
			type: 'complex-genres-tags',
			label: 'Genres',
			default: { mode: 'basic', included: [], excluded: [], optional: [], percentages: {}, counts: {} }
		},
		'tags': {
			type: 'complex-genres-tags',
			label: 'Tags',
			default: { mode: 'basic', included: [], excluded: [], optional: [], percentages: {}, counts: {} }
		}
	};

	// Get configuration for current node
	const config = $derived(nodeData ? settingConfigs[nodeData.id?.replace('-setting', '')] : null);

	// Validation function
	function validateValue(value, config) {
		if (!config) return { valid: true, message: '' };

		switch (config.type) {
			case 'number':
				const num = Number(value);
				if (isNaN(num) || num < config.min || num > config.max) {
					return { valid: false, message: `Value must be between ${config.min} and ${config.max}` };
				}
				break;
			case 'number-with-random':
				if (typeof value === 'object' && value.random) {
					if (value.min < config.min || value.max > config.max || value.min > value.max) {
						return { valid: false, message: `Range must be between ${config.min}-${config.max} and min ≤ max` };
					}
				} else if (typeof value === 'object' && value.value !== undefined) {
					const num = Number(value.value);
					if (isNaN(num) || num < config.min || num > config.max) {
						return { valid: false, message: `Value must be between ${config.min} and ${config.max}` };
					}
				} else {
					const num = Number(value);
					if (isNaN(num) || num < config.min || num > config.max) {
						return { valid: false, message: `Value must be between ${config.min} and ${config.max}` };
					}
				}
				break;
			case 'range':
				if (value.start < config.min || value.end > config.max || value.start > value.end) {
					return { valid: false, message: `Range must be between ${config.min}-${config.max}% and start ≤ end` };
				}
				break;
		}
		return { valid: true, message: '' };
	}

	// Update validation when editedValue changes
	$effect(() => {
		if (editedValue && config) {
			const validation = validateValue(editedValue, config);
			isValid = validation.valid;
			validationMessage = validation.message;
		}
	});

	function handleSave() {
		if (!isValid || !nodeData) return;
		
		onSave({
			nodeId: nodeData.id,
			newValue: editedValue
		});
		
		open = false;
	}

	function handleCancel() {
		open = false;
		editedValue = null;
	}
</script>

<Dialog bind:open>
	<DialogContent class="fixed inset-0 w-screen h-screen p-0 m-0 overflow-hidden bg-white border-0 shadow-none fullscreen-modal max-w-none max-h-none" showCloseButton={false}>
		<div class="flex flex-col w-full h-full dialog-inner-content">
			<!-- Header with background color matching node -->
			<div class="flex-shrink-0 px-8 py-6 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100">
			<DialogHeader class="text-center">
				<DialogTitle class="flex items-center justify-center mb-2 text-3xl text-gray-900">
					<span class="mr-4 text-4xl" style="color: {getNodeColor()}">{nodeData?.icon}</span>
					Edit {nodeData?.title}
				</DialogTitle>
				<DialogDescription class="text-lg text-gray-600">
					Configure the settings for this node
				</DialogDescription>
			</DialogHeader>
		</div>

		<!-- Scrollable content area -->
		<div class="flex-1 overflow-y-auto bg-gradient-to-br from-gray-50 to-gray-100">
			{#if config && editedValue !== null}
				<div class="min-h-full p-8 space-y-8">
				{#if config.type === 'select'}
					<div class="space-y-2">
						<Label for="setting-value">{config.label}</Label>
						<Select.Root bind:value={editedValue}>
							<Select.Trigger class="w-full">
								{editedValue ? config.options.find(opt => opt.value === editedValue)?.label || editedValue : "Select an option..."}
							</Select.Trigger>
							<Select.Content>
								{#each config.options as option}
									<Select.Item value={option.value}>{option.label}</Select.Item>
								{/each}
							</Select.Content>
						</Select.Root>
					</div>

				{:else if config.type === 'number'}
					<div class="space-y-2">
						<Label for="setting-value">{config.label}</Label>
						<Input
							id="setting-value"
							type="number"
							bind:value={editedValue}
							min={config.min}
							max={config.max}
							class={!isValid ? 'border-red-500' : ''}
						/>
						{#if !isValid}
							<p class="text-sm text-red-500">{validationMessage}</p>
						{/if}
					</div>

				{:else if config.type === 'number-with-random'}
					<div class="space-y-4">
						<Label>{config.label}</Label>

						<div class="flex items-center space-x-2">
							<Checkbox
								bind:checked={editedValue.random}
								id="random-toggle"
							/>
							<Label for="random-toggle" class="text-sm">Use random range</Label>
						</div>

						{#if editedValue.random}
							<div class="grid grid-cols-2 gap-2">
								<div>
									<Label for="min-value" class="text-sm">Min</Label>
									<Input
										id="min-value"
										type="number"
										bind:value={editedValue.min}
										min={config.min}
										max={config.max}
										class={!isValid ? 'border-red-500' : ''}
									/>
								</div>
								<div>
									<Label for="max-value" class="text-sm">Max</Label>
									<Input
										id="max-value"
										type="number"
										bind:value={editedValue.max}
										min={config.min}
										max={config.max}
										class={!isValid ? 'border-red-500' : ''}
									/>
								</div>
							</div>
						{:else}
							<Input
								type="number"
								bind:value={editedValue.value}
								min={config.min}
								max={config.max}
								class={!isValid ? 'border-red-500' : ''}
							/>
						{/if}

						{#if !isValid}
							<p class="text-sm text-red-500">{validationMessage}</p>
						{/if}
					</div>

				{:else if config.type === 'range'}
					<div class="space-y-8">
						<div class="text-center">
							<Label class="text-2xl font-bold text-gray-800">{config.label}</Label>
							<p class="mt-2 text-gray-600">Use the slider below to set the sample point range for your quiz</p>
						</div>

						<div class="space-y-6">
							<div class="px-8 py-12 bg-white border-2 border-gray-200 rounded-lg shadow-lg">
								<RangeSlider
									values={[editedValue.start || config.min, editedValue.end || config.max]}
									min={config.min}
									max={config.max}
									step={1}
									range
									pushy
									pips
									pipstep={10}
									all="label"
									on:change={(e) => {
										editedValue.start = e.detail.values[0];
										editedValue.end = e.detail.values[1];
									}}
									--slider={getNodeColor()}
									--handle={getNodeColor()}
									--range={getNodeColor()}
									--progress={getNodeColor()}
									--pip={getNodeColor()}
									--pip-text="#6366f1"
									--pip-active="#6366f1"
									--pip-hover="#6366f1"
								/>
							</div>

							<div class="grid grid-cols-3 gap-4 text-center">
								<div class="p-4 bg-white border rounded-lg">
									<div class="text-sm text-gray-600">Start</div>
									<div class="text-2xl font-bold" style="color: {getNodeColor()}">{editedValue.start || config.min}%</div>
								</div>
								<div class="p-4 bg-white border rounded-lg">
									<div class="text-sm text-gray-600">End</div>
									<div class="text-2xl font-bold" style="color: {getNodeColor()}">{editedValue.end || config.max}%</div>
								</div>
								<div class="p-4 bg-white border rounded-lg">
									<div class="text-sm text-gray-600">Range</div>
									<div class="text-2xl font-bold" style="color: {getNodeColor()}">{(editedValue.end || config.max) - (editedValue.start || config.min)}%</div>
								</div>
							</div>
						</div>

						{#if !isValid}
							<p class="text-sm text-red-500">{validationMessage}</p>
						{/if}
					</div>

				{:else if config.type === 'checkboxes'}
					<div class="space-y-3">
						<Label>{config.label}</Label>
						{#each config.options as option}
							<div class="flex items-center space-x-2">
								<Checkbox
									bind:checked={editedValue[option.key]}
									id={option.key}
								/>
								<Label for={option.key} class="text-sm">{option.label}</Label>
							</div>
						{/each}
					</div>

				{:else if config.type === 'select-with-random'}
					<div class="space-y-4">
						<Label>{config.label}</Label>

						<div class="flex items-center space-x-2">
							<Checkbox
								bind:checked={editedValue.random}
								id="random-speed-toggle"
							/>
							<Label for="random-speed-toggle" class="text-sm">Use random range</Label>
						</div>

						{#if editedValue.random}
							<div class="space-y-2">
								<Label class="text-sm">Select allowed options:</Label>
								{#each config.options as option}
									<div class="flex items-center space-x-2">
										<Checkbox
											bind:checked={editedValue.options[option.value]}
											id={`option-${option.value}`}
										/>
										<Label for={`option-${option.value}`} class="text-sm">{option.label}</Label>
									</div>
								{/each}
							</div>
						{:else}
							<Select.Root bind:value={editedValue.value}>
								<Select.Trigger class="w-full">
									{editedValue.value ? config.options.find(opt => opt.value === editedValue.value)?.label || editedValue.value : "Select an option..."}
								</Select.Trigger>
								<Select.Content>
									{#each config.options as option}
										<Select.Item value={option.value}>{option.label}</Select.Item>
									{/each}
								</Select.Content>
							</Select.Root>
						{/if}
					</div>

				{:else if config.type === 'complex-song-types-selection'}
					<div class="space-y-8">
						<div class="text-center">
							<Label class="text-2xl font-bold text-gray-800">{config.label}</Label>
							<p class="mt-2 text-gray-600">Configure song types and selection preferences</p>
						</div>

						<div class="grid grid-cols-1 gap-8 lg:grid-cols-2">
							<!-- Song Types Section -->
							<div class="p-6 bg-white border border-gray-200 rounded-lg shadow-lg">
								<Label class="block mb-4 text-lg font-semibold text-gray-800">Song Types</Label>
								<div class="space-y-6">
									<div class="space-y-2">
										<div class="flex items-center space-x-2">
											<Checkbox bind:checked={editedValue.songTypes.openings.enabled} id="openings" />
											<Label for="openings" class="text-sm">Openings</Label>
										</div>
										{#if editedValue.songTypes.openings.enabled}
											<div class="px-2">
												<RangeSlider
													values={[editedValue.songTypes.openings.percentage]}
													min={0}
													max={100}
													step={1}
													pips
													pipstep={25}
													all="label"
													on:change={(e) => editedValue.songTypes.openings.percentage = e.detail.value}
													--slider={getNodeColor()}
													--handle={getNodeColor()}
													--range={getNodeColor()}
													--progress={getNodeColor()}
												/>
												<div class="mt-1 text-xs text-center text-gray-600">{editedValue.songTypes.openings.percentage}%</div>
											</div>
										{/if}
									</div>
									<div class="space-y-2">
										<div class="flex items-center space-x-2">
											<Checkbox bind:checked={editedValue.songTypes.endings.enabled} id="endings" />
											<Label for="endings" class="text-sm">Endings</Label>
										</div>
										{#if editedValue.songTypes.endings.enabled}
											<div class="px-2">
												<RangeSlider
													values={[editedValue.songTypes.endings.percentage]}
													min={0}
													max={100}
													step={1}
													pips
													pipstep={25}
													all="label"
													on:change={(e) => editedValue.songTypes.endings.percentage = e.detail.value}
													--slider={getNodeColor()}
													--handle={getNodeColor()}
													--range={getNodeColor()}
													--progress={getNodeColor()}
												/>
												<div class="mt-1 text-xs text-center text-gray-600">{editedValue.songTypes.endings.percentage}%</div>
											</div>
										{/if}
									</div>
									<div class="space-y-2">
										<div class="flex items-center space-x-2">
											<Checkbox bind:checked={editedValue.songTypes.inserts.enabled} id="inserts" />
											<Label for="inserts" class="text-sm">Insert Songs</Label>
										</div>
										{#if editedValue.songTypes.inserts.enabled}
											<div class="px-2">
												<RangeSlider
													values={[editedValue.songTypes.inserts.percentage]}
													min={0}
													max={100}
													step={1}
													pips
													pipstep={25}
													all="label"
													on:change={(e) => editedValue.songTypes.inserts.percentage = e.detail.value}
													--slider={getNodeColor()}
													--handle={getNodeColor()}
													--range={getNodeColor()}
													--progress={getNodeColor()}
												/>
												<div class="mt-1 text-xs text-center text-gray-600">{editedValue.songTypes.inserts.percentage}%</div>
											</div>
										{/if}
									</div>
								</div>
							</div>

							<!-- Song Selection Section -->
							<div class="p-6 bg-white border border-gray-200 rounded-lg shadow-lg">
								<Label class="block mb-4 text-lg font-semibold text-gray-800">Song Selection</Label>
								<div class="space-y-6">
									<div class="space-y-2">
										<Label for="random-pct" class="text-xs">Random</Label>
										<div class="px-2">
											<RangeSlider
												values={[editedValue.songSelection.random]}
												min={0}
												max={100}
												step={1}
												pips
												pipstep={25}
												all="label"
												on:change={(e) => editedValue.songSelection.random = e.detail.value}
												--slider={getNodeColor()}
												--handle={getNodeColor()}
												--range={getNodeColor()}
												--progress={getNodeColor()}
											/>
											<div class="mt-1 text-xs text-center text-gray-600">{editedValue.songSelection.random}%</div>
										</div>
									</div>
									<div class="space-y-2">
										<Label for="mix-pct" class="text-xs">Mix</Label>
										<div class="px-2">
											<RangeSlider
												values={[editedValue.songSelection.mix]}
												min={0}
												max={100}
												step={1}
												pips
												pipstep={25}
												all="label"
												on:change={(e) => editedValue.songSelection.mix = e.detail.value}
												--slider={getNodeColor()}
												--handle={getNodeColor()}
												--range={getNodeColor()}
												--progress={getNodeColor()}
											/>
											<div class="mt-1 text-xs text-center text-gray-600">{editedValue.songSelection.mix}%</div>
										</div>
									</div>
									<div class="space-y-2">
										<Label for="watched-pct" class="text-xs">Watched</Label>
										<div class="px-2">
											<RangeSlider
												values={[editedValue.songSelection.watched]}
												min={0}
												max={100}
												step={1}
												pips
												pipstep={25}
												all="label"
												on:change={(e) => editedValue.songSelection.watched = e.detail.value}
												--slider={getNodeColor()}
												--handle={getNodeColor()}
												--range={getNodeColor()}
												--progress={getNodeColor()}
											/>
											<div class="mt-1 text-xs text-center text-gray-600">{editedValue.songSelection.watched}%</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>

				{:else if config.type === 'complex-song-categories'}
					<div class="space-y-8">
						<div class="text-center">
							<Label class="text-2xl font-bold text-gray-800">{config.label}</Label>
							<p class="mt-2 text-gray-600">Configure which song categories to include</p>
						</div>

						<div class="p-6 bg-white border border-gray-200 rounded-lg shadow-lg">
							<div class="flex items-center justify-center mb-6 space-x-4">
								<Label class="text-lg font-medium">Mode:</Label>
								<Select.Root bind:value={editedValue.mode}>
									<Select.Trigger class="w-48 text-base">
										{editedValue.mode === 'all' ? 'All Enabled' : editedValue.mode === 'counts' ? 'Specific Counts' : 'Percentages'}
									</Select.Trigger>
									<Select.Content>
										<Select.Item value="all">All Enabled</Select.Item>
										<Select.Item value="counts">Specific Counts</Select.Item>
										<Select.Item value="percentages">Percentages</Select.Item>
									</Select.Content>
								</Select.Root>
							</div>

							<div class="grid grid-cols-1 gap-8 lg:grid-cols-3">
								<div class="p-4 space-y-4 bg-white border rounded-lg">
									<Label class="block text-lg font-medium text-center" style="color: {getNodeColor()}">Openings</Label>
									<div class="space-y-2">
										<div class="flex items-center space-x-2">
											<Checkbox bind:checked={editedValue.openings.standard} id="op-standard" />
											<Label for="op-standard" class="text-xs">Standard</Label>
										</div>
										<div class="flex items-center space-x-2">
											<Checkbox bind:checked={editedValue.openings.instrumental} id="op-instrumental" />
											<Label for="op-instrumental" class="text-xs">Instrumental</Label>
										</div>
										<div class="flex items-center space-x-2">
											<Checkbox bind:checked={editedValue.openings.chanting} id="op-chanting" />
											<Label for="op-chanting" class="text-xs">Chanting</Label>
										</div>
										<div class="flex items-center space-x-2">
											<Checkbox bind:checked={editedValue.openings.character} id="op-character" />
											<Label for="op-character" class="text-xs">Character</Label>
										</div>
									</div>
									{#if editedValue.mode === 'counts'}
										<Input type="number" bind:value={editedValue.counts.openings} min="0" placeholder="Count" class="text-xs" />
									{:else if editedValue.mode === 'percentages'}
										<div class="px-2">
											<RangeSlider
												values={[editedValue.percentages.openings]}
												min={0}
												max={100}
												step={1}
												pips
												pipstep={25}
												all="label"
												on:change={(e) => editedValue.percentages.openings = e.detail.value}
												--slider={getNodeColor()}
												--handle={getNodeColor()}
												--range={getNodeColor()}
												--progress={getNodeColor()}
											/>
											<div class="mt-1 text-xs text-center text-gray-600">{editedValue.percentages.openings}%</div>
										</div>
									{/if}
								</div>

								<div class="p-4 space-y-4 bg-white border rounded-lg">
									<Label class="block text-lg font-medium text-center" style="color: {getNodeColor()}">Endings</Label>
									<div class="space-y-2">
										<div class="flex items-center space-x-2">
											<Checkbox bind:checked={editedValue.endings.standard} id="ed-standard" />
											<Label for="ed-standard" class="text-xs">Standard</Label>
										</div>
										<div class="flex items-center space-x-2">
											<Checkbox bind:checked={editedValue.endings.instrumental} id="ed-instrumental" />
											<Label for="ed-instrumental" class="text-xs">Instrumental</Label>
										</div>
										<div class="flex items-center space-x-2">
											<Checkbox bind:checked={editedValue.endings.chanting} id="ed-chanting" />
											<Label for="ed-chanting" class="text-xs">Chanting</Label>
										</div>
										<div class="flex items-center space-x-2">
											<Checkbox bind:checked={editedValue.endings.character} id="ed-character" />
											<Label for="ed-character" class="text-xs">Character</Label>
										</div>
									</div>
									{#if editedValue.mode === 'counts'}
										<Input type="number" bind:value={editedValue.counts.endings} min="0" placeholder="Count" class="text-xs" />
									{:else if editedValue.mode === 'percentages'}
										<div class="px-2">
											<RangeSlider
												values={[editedValue.percentages.endings]}
												min={0}
												max={100}
												step={1}
												pips
												pipstep={25}
												all="label"
												on:change={(e) => editedValue.percentages.endings = e.detail.value}
												--slider={getNodeColor()}
												--handle={getNodeColor()}
												--range={getNodeColor()}
												--progress={getNodeColor()}
											/>
											<div class="mt-1 text-xs text-center text-gray-600">{editedValue.percentages.endings}%</div>
										</div>
									{/if}
								</div>

								<div class="p-4 space-y-4 bg-white border rounded-lg">
									<Label class="block text-lg font-medium text-center" style="color: {getNodeColor()}">Insert Songs</Label>
									<div class="space-y-2">
										<div class="flex items-center space-x-2">
											<Checkbox bind:checked={editedValue.inserts.standard} id="ins-standard" />
											<Label for="ins-standard" class="text-xs">Standard</Label>
										</div>
										<div class="flex items-center space-x-2">
											<Checkbox bind:checked={editedValue.inserts.instrumental} id="ins-instrumental" />
											<Label for="ins-instrumental" class="text-xs">Instrumental</Label>
										</div>
										<div class="flex items-center space-x-2">
											<Checkbox bind:checked={editedValue.inserts.chanting} id="ins-chanting" />
											<Label for="ins-chanting" class="text-xs">Chanting</Label>
										</div>
										<div class="flex items-center space-x-2">
											<Checkbox bind:checked={editedValue.inserts.character} id="ins-character" />
											<Label for="ins-character" class="text-xs">Character</Label>
										</div>
									</div>
									{#if editedValue.mode === 'counts'}
										<Input type="number" bind:value={editedValue.counts.inserts} min="0" placeholder="Count" class="text-xs" />
									{:else if editedValue.mode === 'percentages'}
										<div class="px-2">
											<RangeSlider
												values={[editedValue.percentages.inserts]}
												min={0}
												max={100}
												step={1}
												pips
												pipstep={25}
												all="label"
												on:change={(e) => editedValue.percentages.inserts = e.detail.value}
												--slider={getNodeColor()}
												--handle={getNodeColor()}
												--range={getNodeColor()}
												--progress={getNodeColor()}
											/>
											<div class="mt-1 text-xs text-center text-gray-600">{editedValue.percentages.inserts}%</div>
										</div>
									{/if}
								</div>
							</div>
						</div>
					</div>
				{:else if config.type === 'complex-song-difficulty'}
					<div class="space-y-8">
						<div class="text-center">
							<Label class="text-2xl font-bold text-gray-800">{config.label}</Label>
							<p class="mt-2 text-gray-600">Configure difficulty distribution for songs</p>
						</div>

						<div class="p-6 bg-white border border-gray-200 rounded-lg shadow-lg">
							<div class="flex items-center justify-center mb-6 space-x-4">
								<Label class="text-lg font-medium">Mode:</Label>
								<Select.Root bind:value={editedValue.mode}>
									<Select.Trigger class="w-48 text-base">
										{editedValue.mode === 'all' ? 'All Enabled' : editedValue.mode === 'counts' ? 'Specific Counts' : 'Percentages'}
									</Select.Trigger>
									<Select.Content>
										<Select.Item value="all">All Enabled</Select.Item>
										<Select.Item value="counts">Specific Counts</Select.Item>
										<Select.Item value="percentages">Percentages</Select.Item>
									</Select.Content>
								</Select.Root>
							</div>

							<div class="grid grid-cols-1 gap-8 lg:grid-cols-3">
								<div class="p-4 space-y-4 bg-white border rounded-lg">
									<div class="flex items-center justify-center space-x-2">
										<Checkbox bind:checked={editedValue.easy.enabled} id="easy" />
										<Label for="easy" class="text-lg font-medium" style="color: {getNodeColor()}">Easy</Label>
									</div>
									{#if editedValue.easy.enabled && editedValue.mode === 'percentages'}
										<div class="px-2">
											<RangeSlider
												values={[editedValue.easy.percentage]}
												min={0}
												max={100}
												step={1}
												pips
												pipstep={25}
												all="label"
												on:change={(e) => editedValue.easy.percentage = e.detail.value}
												--slider={getNodeColor()}
												--handle={getNodeColor()}
												--range={getNodeColor()}
												--progress={getNodeColor()}
											/>
											<div class="mt-1 text-xs text-center text-gray-600">{editedValue.easy.percentage}%</div>
										</div>
									{:else if editedValue.easy.enabled && editedValue.mode === 'counts'}
										<Input type="number" bind:value={editedValue.easy.count} min="0" class="text-xs" />
									{/if}
								</div>
								<div class="p-4 space-y-4 bg-white border rounded-lg">
									<div class="flex items-center justify-center space-x-2">
										<Checkbox bind:checked={editedValue.medium.enabled} id="medium" />
										<Label for="medium" class="text-lg font-medium" style="color: {getNodeColor()}">Medium</Label>
									</div>
									{#if editedValue.medium.enabled && editedValue.mode === 'percentages'}
										<div class="px-2">
											<RangeSlider
												values={[editedValue.medium.percentage]}
												min={0}
												max={100}
												step={1}
												pips
												pipstep={25}
												all="label"
												on:change={(e) => editedValue.medium.percentage = e.detail.value}
												--slider={getNodeColor()}
												--handle={getNodeColor()}
												--range={getNodeColor()}
												--progress={getNodeColor()}
											/>
											<div class="mt-1 text-xs text-center text-gray-600">{editedValue.medium.percentage}%</div>
										</div>
									{:else if editedValue.medium.enabled && editedValue.mode === 'counts'}
										<Input type="number" bind:value={editedValue.medium.count} min="0" class="text-xs" />
									{/if}
								</div>
								<div class="p-4 space-y-4 bg-white border rounded-lg">
									<div class="flex items-center justify-center space-x-2">
										<Checkbox bind:checked={editedValue.hard.enabled} id="hard" />
										<Label for="hard" class="text-lg font-medium" style="color: {getNodeColor()}">Hard</Label>
									</div>
									{#if editedValue.hard.enabled && editedValue.mode === 'percentages'}
										<div class="px-2">
											<RangeSlider
												values={[editedValue.hard.percentage]}
												min={0}
												max={100}
												step={1}
												pips
												pipstep={25}
												all="label"
												on:change={(e) => editedValue.hard.percentage = e.detail.value}
												--slider={getNodeColor()}
												--handle={getNodeColor()}
												--range={getNodeColor()}
												--progress={getNodeColor()}
											/>
											<div class="mt-1 text-xs text-center text-gray-600">{editedValue.hard.percentage}%</div>
										</div>
									{:else if editedValue.hard.enabled && editedValue.mode === 'counts'}
										<Input type="number" bind:value={editedValue.hard.count} min="0" class="text-xs" />
									{/if}
								</div>
							</div>
						</div>
					</div>

				{:else if config.type === 'complex-score-range'}
					<div class="space-y-6">
						<Label class="text-lg font-semibold">{config.label}</Label>

						<div class="space-y-4">
							<div class="flex items-center space-x-4">
								<Label class="text-sm font-medium">Mode:</Label>
								<Select.Root bind:value={editedValue.mode}>
									<Select.Trigger class="w-32">
										{editedValue.mode === 'range' ? 'Range' : 'Percentages'}
									</Select.Trigger>
									<Select.Content>
										<Select.Item value="range">Range</Select.Item>
										<Select.Item value="percentages">Percentages</Select.Item>
									</Select.Content>
								</Select.Root>
							</div>

							{#if editedValue.mode === 'range'}
								<div class="flex items-center space-x-4">
									<div class="space-y-1">
										<Label for="min-score" class="text-xs">Min Score</Label>
										<Input id="min-score" type="number" bind:value={editedValue.min} min={config.min} max={config.max} class="w-20 text-xs" />
									</div>
									<div class="space-y-1">
										<Label for="max-score" class="text-xs">Max Score</Label>
										<Input id="max-score" type="number" bind:value={editedValue.max} min={config.min} max={config.max} class="w-20 text-xs" />
									</div>
								</div>
							{:else}
								<div class="grid grid-cols-2 gap-4 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8">
									{#each Array.from({length: config.max - config.min + 1}, (_, i) => config.min + i) as score}
										<div class="space-y-2">
											<Label for="score-{score}" class="text-xs">Score {score}</Label>
											<div class="px-2">
												<RangeSlider
													values={[editedValue.percentages[score] || 0]}
													min={0}
													max={100}
													step={1}
													pips
													pipstep={25}
													all="label"
													on:change={(e) => editedValue.percentages[score] = e.detail.value}
													--slider={getNodeColor()}
													--handle={getNodeColor()}
													--range={getNodeColor()}
													--progress={getNodeColor()}
												/>
												<div class="mt-1 text-xs text-center text-gray-600">{editedValue.percentages[score] || 0}%</div>
											</div>
										</div>
									{/each}
								</div>
							{/if}
						</div>
					</div>
				{:else if config.type === 'complex-vintage'}
					<div class="space-y-6">
						<Label class="text-lg font-semibold">{config.label}</Label>

						<div class="space-y-4">
							<div class="space-y-3">
								{#each editedValue.ranges as range, index}
									<div class="p-4 space-y-3 border rounded-lg">
										<div class="flex items-center justify-between">
											<Label class="text-sm font-medium">Range {index + 1}</Label>
											{#if editedValue.ranges.length > 1}
												<Button variant="outline" size="sm" onclick={() => {
													editedValue.ranges = editedValue.ranges.filter((_, i) => i !== index);
												}}>Remove</Button>
											{/if}
										</div>

										<div class="grid grid-cols-2 gap-4">
											<div class="space-y-2">
												<Label class="text-xs">From</Label>
												<div class="flex space-x-2">
													<Select.Root bind:value={range.from.season}>
														<Select.Trigger class="w-24">
															{range.from.season}
														</Select.Trigger>
														<Select.Content>
															<Select.Item value="Winter">Winter</Select.Item>
															<Select.Item value="Spring">Spring</Select.Item>
															<Select.Item value="Summer">Summer</Select.Item>
															<Select.Item value="Fall">Fall</Select.Item>
														</Select.Content>
													</Select.Root>
													<Input type="number" bind:value={range.from.year} min="1944" max="2025" class="w-20 text-xs" />
												</div>
											</div>
											<div class="space-y-2">
												<Label class="text-xs">To</Label>
												<div class="flex space-x-2">
													<Select.Root bind:value={range.to.season}>
														<Select.Trigger class="w-24">
															{range.to.season}
														</Select.Trigger>
														<Select.Content>
															<Select.Item value="Winter">Winter</Select.Item>
															<Select.Item value="Spring">Spring</Select.Item>
															<Select.Item value="Summer">Summer</Select.Item>
															<Select.Item value="Fall">Fall</Select.Item>
														</Select.Content>
													</Select.Root>
													<Input type="number" bind:value={range.to.year} min="1944" max="2025" class="w-20 text-xs" />
												</div>
											</div>
										</div>
									</div>
								{/each}
							</div>

							<Button variant="outline" onclick={() => {
								editedValue.ranges = [...editedValue.ranges, { from: { season: 'Winter', year: 2020 }, to: { season: 'Fall', year: 2025 } }];
							}}>Add Range</Button>
						</div>
					</div>

				{:else if config.type === 'complex-genres-tags'}
					<div class="space-y-6">
						<Label class="text-lg font-semibold">{config.label}</Label>

						<div class="space-y-4">
							<div class="flex items-center space-x-4">
								<Label class="text-sm font-medium">Mode:</Label>
								<Select.Root bind:value={editedValue.mode}>
									<Select.Trigger class="w-32">
										{editedValue.mode === 'basic' ? 'Basic' : 'Advanced'}
									</Select.Trigger>
									<Select.Content>
										<Select.Item value="basic">Basic</Select.Item>
										<Select.Item value="advanced">Advanced</Select.Item>
									</Select.Content>
								</Select.Root>
							</div>

							{#if editedValue.mode === 'basic'}
								<div class="grid grid-cols-3 gap-4">
									<div class="space-y-2">
										<Label class="text-sm font-medium text-green-600">Included</Label>
										<div class="min-h-[100px] border rounded p-2 text-xs text-gray-500">
											{editedValue.included.length > 0 ? editedValue.included.join(', ') : 'None selected'}
										</div>
									</div>
									<div class="space-y-2">
										<Label class="text-sm font-medium text-red-600">Excluded</Label>
										<div class="min-h-[100px] border rounded p-2 text-xs text-gray-500">
											{editedValue.excluded.length > 0 ? editedValue.excluded.join(', ') : 'None selected'}
										</div>
									</div>
									<div class="space-y-2">
										<Label class="text-sm font-medium text-blue-600">Optional</Label>
										<div class="min-h-[100px] border rounded p-2 text-xs text-gray-500">
											{editedValue.optional.length > 0 ? editedValue.optional.join(', ') : 'None selected'}
										</div>
									</div>
								</div>
							{:else}
								<div class="space-y-3">
									<Label class="text-sm font-medium">Advanced Editor</Label>
									<div class="text-sm text-gray-600">
										Advanced editor with percentage and count controls would be implemented here.
									</div>
								</div>
							{/if}
						</div>
					</div>
				{/if}
			</div>
		{/if}
		</div>

		<!-- Fixed footer -->
		<div class="flex-shrink-0 px-8 py-6 border-t border-gray-200 bg-gray-50">
			<DialogFooter class="flex justify-center gap-4">
				<Button variant="outline" onclick={handleCancel} class="px-8 py-3 text-lg">Cancel</Button>
				<Button onclick={handleSave} disabled={!isValid} class="px-8 py-3 text-lg" style="background-color: {getNodeColor()}; border-color: {getNodeColor()};">Save Changes</Button>
			</DialogFooter>
		</div>
	</div>
</DialogContent>
</Dialog>

<style>
	/* Force fullscreen modal - target the specific dialog content */
	:global(.fullscreen-modal),
	:global([data-slot="dialog-content"].fullscreen-modal) {
		width: 100vw !important;
		height: 100vh !important;
		max-width: none !important;
		max-height: none !important;
		margin: 0 !important;
		padding: 0 !important;
		border-radius: 0 !important;
		position: fixed !important;
		top: 0 !important;
		left: 0 !important;
		right: 0 !important;
		bottom: 0 !important;
		transform: none !important;
		inset: 0 !important;
		translate: none !important;
		border: none !important;
		box-shadow: none !important;
	}

	/* Disable animations that might interfere with positioning */
	:global([data-slot="dialog-content"].fullscreen-modal) {
		animation: none !important;
	}

	:global([data-slot="dialog-content"].fullscreen-modal[data-state="open"]) {
		animation: none !important;
		transform: none !important;
		translate: none !important;
	}

	/* Smooth fade-in for content only */
	:global(.fullscreen-modal .dialog-inner-content) {
		animation: modalFadeIn 0.3s ease-out;
	}

	@keyframes modalFadeIn {
		from {
			opacity: 0;
		}
		to {
			opacity: 1;
		}
	}

	/* Enhanced slider styling */
	:global(.rangeSlider) {
		margin: 1.5rem 0;
	}

	:global(.rangeSlider .rangeHandle) {
		transition: all 0.2s ease;
	}

	:global(.rangeSlider .rangeHandle:hover) {
		transform: scale(1.1);
	}

	/* Hide the overlay background only when fullscreen modal is present */
	:global(body:has(.fullscreen-modal) [data-slot="dialog-overlay"]) {
		background: transparent !important;
		backdrop-filter: none !important;
		opacity: 0 !important;
		pointer-events: none !important;
	}

	/* Alternative selector for browsers that don't support :has() */
	:global(.fullscreen-modal ~ [data-slot="dialog-overlay"]),
	:global([data-slot="dialog-overlay"]:has(~ .fullscreen-modal)) {
		background: transparent !important;
		backdrop-filter: none !important;
		opacity: 0 !important;
		pointer-events: none !important;
	}

	/* Force dialog portal to be fullscreen */
	:global([data-dialog-portal]) {
		position: fixed !important;
		inset: 0 !important;
		width: 100vw !important;
		height: 100vh !important;
		margin: 0 !important;
		padding: 0 !important;
	}

	/* Override bits-ui dialog positioning */
	:global([data-slot="dialog-content"]) {
		width: 100vw !important;
		height: 100vh !important;
		max-width: none !important;
		max-height: none !important;
		margin: 0 !important;
		padding: 0 !important;
		border-radius: 0 !important;
		position: fixed !important;
		top: 0 !important;
		left: 0 !important;
		right: 0 !important;
		bottom: 0 !important;
		transform: none !important;
		translate: none !important;
		inset: 0 !important;
		border: none !important;
		box-shadow: none !important;
	}

	/* Target the specific fullscreen modal class */
	:global([data-slot="dialog-content"].fullscreen-modal) {
		width: 100vw !important;
		height: 100vh !important;
		max-width: none !important;
		max-height: none !important;
		margin: 0 !important;
		padding: 0 !important;
		border-radius: 0 !important;
		position: fixed !important;
		top: 0 !important;
		left: 0 !important;
		right: 0 !important;
		bottom: 0 !important;
		transform: none !important;
		translate: none !important;
		inset: 0 !important;
		border: none !important;
		box-shadow: none !important;
	}

	/* Prevent body scroll when modal is open */
	:global(body:has(.fullscreen-modal)) {
		overflow: hidden !important;
	}

	/* Ultimate override for fullscreen positioning */
	:global(.fullscreen-modal) {
		position: fixed !important;
		top: 0 !important;
		left: 0 !important;
		width: 100vw !important;
		height: 100vh !important;
		max-width: none !important;
		max-height: none !important;
		transform: none !important;
		translate: none !important;
		margin: 0 !important;
		padding: 0 !important;
		border: none !important;
		border-radius: 0 !important;
		box-shadow: none !important;
		z-index: 9999 !important;
	}

	/* Hide overlay for all dialogs when this component is mounted */
	:global([data-slot="dialog-overlay"]) {
		display: none !important;
	}
</style>